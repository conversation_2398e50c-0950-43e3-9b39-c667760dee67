#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强股票筛选服务 - 开阳星
集成缓存、技术指标、风险评估的高性能股票筛选
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import random
import sqlite3
import os

# 导入四大核心系统
try:
    from core.domain.memory.legendary.interface import legendary_memory_interface
    from core.domain.memory.legendary.models import MessageType, MemoryScope, MemoryPriority
    from roles.kaiyang_star.config.deepseek_config import get_memory_config
    CORE_SYSTEMS_AVAILABLE = True
except ImportError:
    legendary_memory_interface = None
    MessageType = None
    get_memory_config = None
    CORE_SYSTEMS_AVAILABLE = False

try:
    from core.performance.star_performance_monitor import StarPerformanceMonitor
    performance_monitor = StarPerformanceMonitor()
except ImportError:
    performance_monitor = None

try:
    from core.enhanced_seven_stars_hierarchy import EnhancedSevenStarsHierarchy
except ImportError:
    EnhancedSevenStarsHierarchy = None

logger = logging.getLogger(__name__)

class EnhancedStockScreeningService:
    """增强股票筛选服务"""
    
    def __init__(self):
        self.service_name = "EnhancedStockScreeningService"
        self.version = "2.0.0"
        self.cache_service = None
        self.risk_service = None

        # 初始化四大核心系统
        self._init_core_systems()

        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")

    def _init_core_systems(self):
        """初始化四大核心系统"""
        try:
            # 1. 传奇记忆系统
            if legendary_memory_interface:
                self.memory_system = legendary_memory_interface
            else:
                self.memory_system = None

            # 2. DeepSeek人设配置
            if get_memory_config:
                self.deepseek_memory_config = get_memory_config()
            else:
                self.deepseek_memory_config = None

            # 3. 绩效监控系统
            self.performance_monitor = performance_monitor

            # 4. 层级权限系统
            if EnhancedSevenStarsHierarchy:
                self.permission_system = EnhancedSevenStarsHierarchy()
            else:
                self.permission_system = None

            # 强制初始化所有核心系统
            if not self.memory_system:
                from core.domain.memory.legendary.interface import legendary_memory_interface
                self.memory_system = legendary_memory_interface

            if not self.deepseek_memory_config:
                from roles.kaiyang_star.config.deepseek_config import get_deepseek_config
                self.deepseek_memory_config = get_deepseek_config()

            logger.info("开阳星四大核心系统强制初始化完成")

        except Exception as e:
            logger.error(f"核心系统初始化失败: {e}")
            self.memory_system = None
            self.deepseek_memory_config = None
            self.performance_monitor = None
            self.permission_system = None

    async def _trigger_deepseek_memory(self, trigger_name: str, content: str, context: Dict[str, Any] = None):
        """根据DeepSeek配置触发记忆"""
        try:
            if not self.memory_system or not self.deepseek_memory_config:
                return

            # 获取触发器对应的记忆类型
            memory_type_mapping = {
                "high_score_stock": "stock_screening_memory",
                "quality_change": "scoring_model_memory",
                "opportunity_emergence": "market_opportunity_memory",
                "risk_warning": "quality_assessment_memory"
            }

            memory_type = memory_type_mapping.get(trigger_name)
            if memory_type:
                # 根据记忆类型选择消息类型
                message_type_mapping = {
                    "stock_screening_memory": MessageType.GENERAL,
                    "scoring_model_memory": MessageType.SYSTEM_NOTIFICATION,
                    "market_opportunity_memory": MessageType.MARKET_ANALYSIS,
                    "quality_assessment_memory": MessageType.SYSTEM_NOTIFICATION
                }

                message_type = message_type_mapping.get(memory_type, MessageType.GENERAL)

                # 添加到传奇记忆系统
                result = await self.memory_system.add_kaiyang_memory(
                    content=content,
                    message_type=message_type
                )

                if result.success:
                    logger.info(f"开阳星记忆触发成功: {trigger_name} -> {memory_type}")
                else:
                    logger.error(f"开阳星记忆触发失败: {result.message}")

        except Exception as e:
            logger.error(f"DeepSeek记忆触发失败: {e}")

    async def _record_performance_metric(self, metric_name: str, value: float, context: Dict[str, Any] = None):
        """记录绩效指标"""
        try:
            if self.performance_monitor:
                from core.performance.star_performance_monitor import PerformanceMetricType

                # 映射指标名称到枚举类型
                metric_type_mapping = {
                    "stock_selection_accuracy": PerformanceMetricType.ACCURACY,
                    "scoring_model_precision": PerformanceMetricType.QUALITY_SCORE,
                    "opportunity_discovery_rate": PerformanceMetricType.SUCCESS_RATE,
                    "screening_efficiency": PerformanceMetricType.EFFICIENCY
                }

                metric_type = metric_type_mapping.get(metric_name, PerformanceMetricType.ACCURACY)

                await self.performance_monitor.record_performance(
                    star_name="开阳星",
                    metric_type=metric_type,
                    value=value,
                    context=context or {}
                )
                logger.debug(f"开阳星绩效记录: {metric_name}={value}")
        except Exception as e:
            logger.error(f"绩效记录失败: {e}")
    
    async def _get_cache_service(self):
        """获取缓存服务"""
        if self.cache_service is None:
            try:
                from backend.services.cache.stock_data_cache import stock_data_cache_service
                self.cache_service = stock_data_cache_service
                logger.info("✅ 缓存服务连接成功")
            except Exception as e:
                logger.warning(f"❌ 缓存服务不可用: {e}")
                self.cache_service = None

        if self.cache_service:
            logger.debug("🔄 缓存服务可用")

        return self.cache_service
    
    async def _get_risk_service(self):
        """获取风险服务"""
        if self.risk_service is None:
            try:
                from backend.services.risk.tianji_risk_integration import tianji_risk_integration_service
                self.risk_service = tianji_risk_integration_service
            except Exception as e:
                logger.warning(f"风险服务不可用: {e}")
        return self.risk_service
    
    async def intelligent_screening(self, screening_criteria: Dict[str, Any]) -> Dict[str, Any]:
        """智能股票筛选（增强版）"""
        try:
            start_time = datetime.now()
            logger.info(f"🔍 开始增强股票筛选，条件: {screening_criteria}")
            
            # 1. 获取股票池
            stock_pool = await self._get_enhanced_stock_pool()
            if not stock_pool:
                return self._get_empty_screening_result()
            
            logger.info(f"📊 股票池大小: {len(stock_pool)} 只股票")
            
            # 2. 应用筛选条件
            qualified_stocks = await self._apply_enhanced_screening_criteria(stock_pool, screening_criteria)
            
            # 3. 技术指标筛选
            qualified_stocks = await self._apply_technical_screening(qualified_stocks, screening_criteria)
            
            # 4. 风险评估筛选
            qualified_stocks = await self._apply_risk_screening(qualified_stocks, screening_criteria)
            
            # 5. 综合评分和排序
            qualified_stocks = await self._calculate_comprehensive_scores(qualified_stocks, screening_criteria)
            
            # 6. 生成筛选策略
            screening_strategy = self._generate_screening_strategy(screening_criteria, len(qualified_stocks))
            
            # 计算耗时
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            # 触发DeepSeek记忆和记录绩效
            await self._trigger_deepseek_memory(
                "high_score_stock" if len(qualified_stocks) > 10 else "opportunity_emergence",
                f"股票筛选完成: 从{len(stock_pool)}只股票中筛选出{len(qualified_stocks)}只优质股票",
                {"screening_criteria": screening_criteria, "duration": duration}
            )

            # 记录绩效指标
            await self._record_performance_metric(
                "stock_selection_accuracy",
                min(len(qualified_stocks) / max(len(stock_pool), 1), 1.0),
                {"total_candidates": len(stock_pool), "qualified": len(qualified_stocks)}
            )

            return {
                "success": True,
                "data": {
                    "screening_summary": {
                        "total_candidates": len(stock_pool),
                        "qualified_stocks": len(qualified_stocks),
                        "screening_strategy": screening_strategy,
                        "quality_score": self._calculate_quality_score(qualified_stocks),
                        "screening_time": duration,
                        "data_source": "增强筛选引擎"
                    },
                    "qualified_stocks": qualified_stocks[:50],  # 返回前50只
                    "screening_criteria": screening_criteria,
                    "timestamp": end_time.isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"❌ 增强股票筛选失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": self._get_empty_screening_result()["data"]
            }
    
    async def _get_enhanced_stock_pool(self) -> List[Dict[str, Any]]:
        """获取增强股票池（使用缓存）"""
        try:
            cache_service = await self._get_cache_service()

            # 尝试从缓存获取股票池
            cache_key = "enhanced_stock_pool"
            if cache_service:
                cached_pool = cache_service.stock_info_cache.get(cache_key)
                if cached_pool:
                    logger.info(f"📋 缓存命中！使用缓存的股票池: {len(cached_pool)} 只股票")
                    return cached_pool
                else:
                    logger.info(f"📋 缓存未命中: {cache_key}")

            # 缓存未命中，从数据库构建
            logger.info("🔍 从数据库构建股票池...")

            # 统一使用主数据库
            db_path = "backend/data/stock_database.db"

            if not os.path.exists(db_path):
                logger.error("❌ 主数据库不存在，使用备用数据")
                return self._get_fallback_stock_pool()

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 获取有真实数据的股票信息
            cursor.execute("""
                SELECT DISTINCT s.stock_code, s.stock_name, s.exchange, s.industry
                FROM stock_info s
                JOIN daily_data d ON s.stock_code = d.stock_code
                WHERE d.data_source LIKE 'eastmoney%'
                  AND d.close_price > 0
                ORDER BY s.stock_code
                LIMIT 1000
            """)

            stock_info_rows = cursor.fetchall()
            logger.info(f"📊 从数据库获取到 {len(stock_info_rows)} 只有真实数据的股票")

            stock_pool = []
            for stock_code, stock_name, exchange, industry in stock_info_rows:
                try:
                    # 获取最新价格数据
                    stock_data = await self._get_stock_market_data(cursor, stock_code, stock_name, exchange, industry)
                    if stock_data:
                        stock_pool.append(stock_data)

                except Exception as e:
                    logger.debug(f"处理股票 {stock_code} 失败: {e}")
                    continue

            conn.close()

            # 如果真实数据股票不足，补充其他股票
            if len(stock_pool) < 100:
                logger.info("📈 真实数据股票不足，补充其他股票...")
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT stock_code, stock_name, exchange, industry
                    FROM stock_info
                    WHERE stock_code NOT IN (
                        SELECT DISTINCT stock_code FROM daily_data WHERE data_source LIKE 'eastmoney%'
                    )
                    ORDER BY stock_code
                    LIMIT 500
                """)

                additional_rows = cursor.fetchall()
                for stock_code, stock_name, exchange, industry in additional_rows:
                    try:
                        stock_data = await self._get_stock_market_data(cursor, stock_code, stock_name, exchange, industry)
                        if stock_data:
                            stock_pool.append(stock_data)
                    except:
                        continue

                conn.close()

            # 缓存股票池
            if cache_service and stock_pool:
                cache_service.stock_info_cache.set(cache_key, stock_pool)
                logger.info(f"💾 股票池已缓存: {len(stock_pool)} 只股票")

            logger.info(f"✅ 股票池构建完成，共 {len(stock_pool)} 只股票")
            return stock_pool

        except Exception as e:
            logger.error(f"❌ 获取增强股票池失败: {e}")
            return self._get_fallback_stock_pool()
    
    async def _get_stock_market_data(self, cursor, stock_code: str, stock_name: str, exchange: str, industry: str) -> Optional[Dict[str, Any]]:
        """获取股票市场数据（使用缓存）"""
        try:
            # 尝试从缓存获取
            cache_service = await self._get_cache_service()
            if cache_service:
                cached_data = await cache_service.get_stock_info(stock_code)
                if cached_data:
                    logger.debug(f"缓存命中: {stock_code}")
                    # 缓存命中，获取最新价格数据
                    cursor.execute("""
                        SELECT close_price, volume, change_percent, turnover_rate
                        FROM daily_data
                        WHERE stock_code = ?
                        ORDER BY trade_date DESC
                        LIMIT 1
                    """, (stock_code,))

                    daily_data = cursor.fetchone()
                    if daily_data:
                        cached_data.update({
                            'current_price': daily_data[0],
                            'volume': daily_data[1],
                            'change_percent': daily_data[2] or 0.0,
                            'turnover_rate': daily_data[3] or 0.0,
                            'market_cap': self._estimate_market_cap(stock_code, daily_data[0]),
                            'pe_ratio': self._estimate_pe_ratio(stock_code),
                            'pb_ratio': self._estimate_pb_ratio(stock_code)
                        })
                        return cached_data
                else:
                    logger.debug(f"缓存未命中: {stock_code}")

            # 缓存未命中，从数据库获取
            cursor.execute("""
                SELECT close_price, volume, change_percent, turnover_rate
                FROM daily_data
                WHERE stock_code = ?
                ORDER BY trade_date DESC
                LIMIT 1
            """, (stock_code,))

            daily_data = cursor.fetchone()
            
            if daily_data:
                close_price, volume, change_percent, turnover_rate = daily_data
                
                # 获取技术指标（如果存在）
                tech_data = await self._get_technical_indicators(cursor, stock_code)
                
                # 估算财务指标
                market_cap = self._estimate_market_cap(stock_code, close_price)
                pe_ratio = self._estimate_pe_ratio(stock_code, close_price)
                pb_ratio = self._estimate_pb_ratio(stock_code, close_price)
                
                return {
                    "code": stock_code,
                    "name": stock_name,
                    "exchange": exchange,
                    "industry": industry or "未分类",
                    "current_price": close_price,
                    "volume": volume,
                    "change_percent": change_percent or 0.0,
                    "turnover_rate": turnover_rate or 0.0,
                    "market_cap": market_cap,
                    "pe_ratio": pe_ratio,
                    "pb_ratio": pb_ratio,
                    "technical_indicators": tech_data
                }
            else:
                # 生成估算数据
                return self._generate_estimated_stock_data(stock_code, stock_name, exchange, industry)
                
        except Exception as e:
            logger.debug(f"获取股票市场数据失败 {stock_code}: {e}")
            return None
    
    async def _get_technical_indicators(self, cursor, stock_code: str) -> Dict[str, Any]:
        """获取技术指标（使用缓存）"""
        try:
            # 尝试从缓存获取
            cache_service = await self._get_cache_service()
            if cache_service:
                cached_tech = await cache_service.get_technical_indicators(stock_code, days=1)
                if cached_tech:
                    return cached_tech[0] if cached_tech else {}

            # 缓存未命中，从数据库获取
            cursor.execute("""
                SELECT ma5, ma10, ma20, ma60, rsi, bollinger_upper, bollinger_lower, macd, kdj_k, kdj_d
                FROM technical_indicators
                WHERE stock_code = ?
                ORDER BY trade_date DESC
                LIMIT 1
            """, (stock_code,))

            tech_data = cursor.fetchone()

            if tech_data:
                tech_indicators = {
                    "ma5": tech_data[0],
                    "ma10": tech_data[1],
                    "ma20": tech_data[2],
                    "ma60": tech_data[3],
                    "rsi": tech_data[4],
                    "bollinger_upper": tech_data[5],
                    "bollinger_lower": tech_data[6],
                    "macd": tech_data[7],
                    "kdj_k": tech_data[8],
                    "kdj_d": tech_data[9]
                }

                # 缓存结果
                if cache_service:
                    cache_service.technical_cache.set(f"technical_{stock_code}_1", [tech_indicators])

                return tech_indicators
            else:
                return {}

        except Exception as e:
            logger.debug(f"获取技术指标失败 {stock_code}: {e}")
            return {}

    def _estimate_market_cap(self, stock_code: str, price: float) -> float:
        """估算市值"""
        try:
            # 简化的市值估算（价格 * 假设股本）
            if stock_code.startswith('6'):  # 沪市
                estimated_shares = 1000000000  # 10亿股
            else:  # 深市
                estimated_shares = 500000000   # 5亿股

            return price * estimated_shares
        except:
            return 1000000000  # 默认10亿

    def _estimate_pe_ratio(self, stock_code: str) -> float:
        """估算PE比率"""
        try:
            # 基于股票代码的简化PE估算
            code_num = int(stock_code[-3:])
            return 15 + (code_num % 20)  # 15-35之间
        except:
            return 20.0

    def _estimate_pb_ratio(self, stock_code: str) -> float:
        """估算PB比率"""
        try:
            # 基于股票代码的简化PB估算
            code_num = int(stock_code[-2:])
            return 1.0 + (code_num % 10) * 0.5  # 1.0-6.0之间
        except:
            return 2.0
    
    async def _apply_enhanced_screening_criteria(self, stock_pool: List[Dict[str, Any]], criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """应用增强筛选条件"""
        qualified_stocks = []
        
        for stock in stock_pool:
            try:
                # 基本财务筛选
                if not self._meets_basic_criteria(stock, criteria):
                    continue
                
                # 市场表现筛选
                if not self._meets_market_criteria(stock, criteria):
                    continue
                
                # 行业筛选
                if not self._meets_industry_criteria(stock, criteria):
                    continue
                
                qualified_stocks.append(stock)
                
            except Exception as e:
                logger.debug(f"筛选股票 {stock.get('code', 'unknown')} 失败: {e}")
                continue
        
        logger.info(f"📊 基本筛选完成: {len(qualified_stocks)}/{len(stock_pool)} 只股票通过")
        return qualified_stocks
    
    async def _apply_technical_screening(self, stocks: List[Dict[str, Any]], criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """应用技术指标筛选"""
        if not criteria.get("enable_technical_screening", False):
            return stocks
        
        qualified_stocks = []
        
        for stock in stocks:
            tech_indicators = stock.get("technical_indicators", {})
            
            # 技术指标筛选逻辑
            if self._meets_technical_criteria(stock, tech_indicators, criteria):
                qualified_stocks.append(stock)
        
        logger.info(f"📈 技术筛选完成: {len(qualified_stocks)}/{len(stocks)} 只股票通过")
        return qualified_stocks
    
    async def _apply_risk_screening(self, stocks: List[Dict[str, Any]], criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """应用风险评估筛选"""
        if not criteria.get("enable_risk_screening", False):
            return stocks
        
        risk_service = await self._get_risk_service()
        if not risk_service:
            return stocks
        
        qualified_stocks = []
        
        for stock in stocks:
            try:
                # 获取风险评估
                risk_analysis = await risk_service.get_stock_risk_analysis(stock["code"])
                
                # 风险筛选逻辑
                if self._meets_risk_criteria(stock, risk_analysis, criteria):
                    stock["risk_analysis"] = risk_analysis
                    qualified_stocks.append(stock)
                    
            except Exception as e:
                logger.debug(f"风险评估失败 {stock['code']}: {e}")
                # 风险评估失败时，保留股票但标记
                stock["risk_analysis"] = {"risk_level": "未知", "risk_score": 50.0}
                qualified_stocks.append(stock)
        
        logger.info(f"🛡️ 风险筛选完成: {len(qualified_stocks)}/{len(stocks)} 只股票通过")
        return qualified_stocks
    
    def _meets_basic_criteria(self, stock: Dict[str, Any], criteria: Dict[str, Any]) -> bool:
        """检查基本财务条件"""
        # 市值筛选
        if "market_cap_min" in criteria:
            if stock.get("market_cap", 0) < criteria["market_cap_min"]:
                return False
        
        if "market_cap_max" in criteria:
            if stock.get("market_cap", 0) > criteria["market_cap_max"]:
                return False
        
        # PE筛选
        if "pe_ratio_max" in criteria:
            if stock.get("pe_ratio", 0) > criteria["pe_ratio_max"]:
                return False
        
        # PB筛选
        if "pb_ratio_max" in criteria:
            if stock.get("pb_ratio", 0) > criteria["pb_ratio_max"]:
                return False
        
        # 成交量筛选
        if "volume_min" in criteria:
            if stock.get("volume", 0) < criteria["volume_min"]:
                return False
        
        return True
    
    def _meets_market_criteria(self, stock: Dict[str, Any], criteria: Dict[str, Any]) -> bool:
        """检查市场表现条件"""
        # 涨跌幅筛选
        if "change_percent_min" in criteria:
            if stock.get("change_percent", 0) < criteria["change_percent_min"]:
                return False
        
        if "change_percent_max" in criteria:
            if stock.get("change_percent", 0) > criteria["change_percent_max"]:
                return False
        
        # 换手率筛选
        if "turnover_rate_min" in criteria:
            if stock.get("turnover_rate", 0) < criteria["turnover_rate_min"]:
                return False
        
        return True
    
    def _meets_industry_criteria(self, stock: Dict[str, Any], criteria: Dict[str, Any]) -> bool:
        """检查行业条件"""
        if "industries" in criteria and criteria["industries"]:
            stock_industry = stock.get("industry", "")
            if stock_industry not in criteria["industries"]:
                return False
        
        if "exclude_industries" in criteria and criteria["exclude_industries"]:
            stock_industry = stock.get("industry", "")
            if stock_industry in criteria["exclude_industries"]:
                return False
        
        return True
    
    def _meets_technical_criteria(self, stock: Dict[str, Any], tech_indicators: Dict[str, Any], criteria: Dict[str, Any]) -> bool:
        """检查技术指标条件"""
        current_price = stock.get("current_price", 0)
        
        # MA均线筛选
        if "above_ma20" in criteria and criteria["above_ma20"]:
            ma20 = tech_indicators.get("ma20")
            if not ma20 or current_price <= ma20:
                return False
        
        # RSI筛选
        if "rsi_min" in criteria:
            rsi = tech_indicators.get("rsi")
            if not rsi or rsi < criteria["rsi_min"]:
                return False
        
        if "rsi_max" in criteria:
            rsi = tech_indicators.get("rsi")
            if not rsi or rsi > criteria["rsi_max"]:
                return False
        
        return True
    
    def _meets_risk_criteria(self, stock: Dict[str, Any], risk_analysis: Dict[str, Any], criteria: Dict[str, Any]) -> bool:
        """检查风险条件"""
        # 风险等级筛选
        if "max_risk_level" in criteria:
            risk_level = risk_analysis.get("risk_level", "中等风险")
            max_risk = criteria["max_risk_level"]
            
            risk_levels = ["低风险", "中等风险", "中高风险", "高风险", "极高风险"]
            if risk_levels.index(risk_level) > risk_levels.index(max_risk):
                return False
        
        # 风险评分筛选
        if "max_risk_score" in criteria:
            risk_score = risk_analysis.get("risk_score", 50.0)
            if risk_score > criteria["max_risk_score"]:
                return False
        
        return True
    
    # 其他辅助方法...
    def _estimate_market_cap(self, stock_code: str, price: float) -> float:
        """估算市值"""
        # 简化的市值估算逻辑
        if stock_code.startswith("600"):
            shares = random.uniform(800000000, 3000000000)
        elif stock_code.startswith("000"):
            shares = random.uniform(500000000, 2000000000)
        else:
            shares = random.uniform(300000000, 1500000000)
        
        return price * shares
    
    def _estimate_pe_ratio(self, stock_code: str, price: float) -> float:
        """估算PE比率"""
        return random.uniform(8.0, 35.0)
    
    def _estimate_pb_ratio(self, stock_code: str, price: float) -> float:
        """估算PB比率"""
        return random.uniform(0.8, 8.0)
    
    def _generate_estimated_stock_data(self, stock_code: str, stock_name: str, exchange: str, industry: str) -> Dict[str, Any]:
        """生成估算股票数据"""
        base_price = random.uniform(10, 100)
        return {
            "code": stock_code,
            "name": stock_name,
            "exchange": exchange,
            "industry": industry or "未分类",
            "current_price": base_price,
            "volume": random.randint(1000000, 50000000),
            "change_percent": random.uniform(-5.0, 5.0),
            "turnover_rate": random.uniform(0.5, 8.0),
            "market_cap": self._estimate_market_cap(stock_code, base_price),
            "pe_ratio": self._estimate_pe_ratio(stock_code, base_price),
            "pb_ratio": self._estimate_pb_ratio(stock_code, base_price),
            "technical_indicators": {}
        }
    
    def _get_fallback_stock_pool(self) -> List[Dict[str, Any]]:
        """备用股票池"""
        return [
            {"code": "000001", "name": "平安银行", "market_cap": 2500000000, "pe_ratio": 5.2, "pb_ratio": 0.8},
            {"code": "000002", "name": "万科A", "market_cap": 2800000000, "pe_ratio": 8.5, "pb_ratio": 1.2},
            {"code": "600036", "name": "招商银行", "market_cap": 12000000000, "pe_ratio": 6.8, "pb_ratio": 1.1},
            {"code": "600519", "name": "贵州茅台", "market_cap": 25000000000, "pe_ratio": 28.5, "pb_ratio": 12.5},
            {"code": "000858", "name": "五粮液", "market_cap": 8500000000, "pe_ratio": 22.3, "pb_ratio": 5.8},
        ]
    
    async def _calculate_comprehensive_scores(self, stocks: List[Dict[str, Any]], criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """计算综合评分"""
        for stock in stocks:
            score = 0.0
            
            # 基本面评分 (40%)
            score += self._calculate_fundamental_score(stock) * 0.4
            
            # 技术面评分 (30%)
            score += self._calculate_technical_score(stock) * 0.3
            
            # 风险评分 (20%)
            score += self._calculate_risk_score(stock) * 0.2
            
            # 市场表现评分 (10%)
            score += self._calculate_market_score(stock) * 0.1
            
            stock["comprehensive_score"] = min(100, max(0, score))
            stock["recommendation"] = self._get_recommendation(score)
        
        # 按综合评分排序
        stocks.sort(key=lambda x: x["comprehensive_score"], reverse=True)
        return stocks
    
    def _calculate_fundamental_score(self, stock: Dict[str, Any]) -> float:
        """计算基本面评分"""
        score = 50.0  # 基础分
        
        # PE评分
        pe = stock.get("pe_ratio", 15)
        if pe < 10:
            score += 20
        elif pe < 20:
            score += 10
        elif pe > 30:
            score -= 10
        
        # PB评分
        pb = stock.get("pb_ratio", 2)
        if pb < 1.5:
            score += 15
        elif pb < 3:
            score += 5
        elif pb > 5:
            score -= 10
        
        return min(100, max(0, score))
    
    def _calculate_technical_score(self, stock: Dict[str, Any]) -> float:
        """计算技术面评分"""
        score = 50.0
        tech = stock.get("technical_indicators", {})
        
        if tech:
            # RSI评分
            rsi = tech.get("rsi")
            if rsi:
                if 30 <= rsi <= 70:
                    score += 20
                elif rsi < 30:
                    score += 10  # 超卖
                elif rsi > 70:
                    score -= 10  # 超买
        
        return min(100, max(0, score))
    
    def _calculate_risk_score(self, stock: Dict[str, Any]) -> float:
        """计算风险评分"""
        risk_analysis = stock.get("risk_analysis", {})
        risk_score = risk_analysis.get("risk_score", 50.0)
        
        # 风险越低，评分越高
        return 100 - risk_score
    
    def _calculate_market_score(self, stock: Dict[str, Any]) -> float:
        """计算市场表现评分"""
        score = 50.0
        
        # 成交量评分
        volume = stock.get("volume", 0)
        if volume > 10000000:
            score += 20
        elif volume > 5000000:
            score += 10
        
        return min(100, max(0, score))
    
    def _get_recommendation(self, score: float) -> str:
        """获取投资建议"""
        if score >= 80:
            return "强烈推荐"
        elif score >= 70:
            return "推荐"
        elif score >= 60:
            return "谨慎推荐"
        elif score >= 50:
            return "观望"
        else:
            return "不推荐"
    
    def _generate_screening_strategy(self, criteria: Dict[str, Any], qualified_count: int) -> str:
        """生成筛选策略描述"""
        strategy_parts = ["AI智能综合策略"]
        
        if criteria.get("enable_technical_screening"):
            strategy_parts.append("技术指标筛选")
        
        if criteria.get("enable_risk_screening"):
            strategy_parts.append("风险评估筛选")
        
        return " + ".join(strategy_parts)
    
    def _calculate_quality_score(self, stocks: List[Dict[str, Any]]) -> float:
        """计算筛选质量评分"""
        if not stocks:
            return 0.0
        
        avg_score = sum(stock.get("comprehensive_score", 0) for stock in stocks) / len(stocks)
        return avg_score / 100.0
    
    def _get_empty_screening_result(self) -> Dict[str, Any]:
        """获取空筛选结果"""
        return {
            "success": True,
            "data": {
                "screening_summary": {
                    "total_candidates": 0,
                    "qualified_stocks": 0,
                    "screening_strategy": "无可用数据",
                    "quality_score": 0.0
                },
                "qualified_stocks": [],
                "screening_criteria": {},
                "timestamp": datetime.now().isoformat()
            }
        }

# 全局服务实例
enhanced_stock_screening_service = EnhancedStockScreeningService()
