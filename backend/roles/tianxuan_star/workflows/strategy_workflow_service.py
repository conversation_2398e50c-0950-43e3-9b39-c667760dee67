#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天璇星-策略架构师智能工作流集合
基于情报官成功模式实现的完整策略研发体系
实现第二阶段：七星工作流集合的核心功能
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field

# 正确的导入路径
from shared.infrastructure.investment_node_service import InvestmentNodeService
from shared.collaboration.investment_message_service import InvestmentMessage, MessageType, RoleType, MessagePriority
from shared.infrastructure.investment_retry_service import smart_retry, RetryStrategy

logger = logging.getLogger(__name__)

class StrategyWorkflowConfig(BaseModel):
    """策略工作流配置"""
    enable_factor_research: bool = True
    enable_model_development: bool = True
    enable_backtest_analysis: bool = True
    enable_model_evaluation: bool = True
    enable_parameter_optimization: bool = True
    enable_strategy_generation: bool = True
    enable_rd_agent: bool = True
    
    # 策略配置
    target_return: float = 0.15
    risk_preference: str = "medium"  # low, medium, high
    strategy_types: List[str] = ["momentum", "mean_reversion", "multi_factor"]
    
    # 优化配置
    optimization_target: str = "sharpe_ratio"
    max_optimization_iterations: int = 50
    confidence_threshold: float = 0.7
    max_concurrent_tasks: int = 3

class StrategyDataPacket(BaseModel):
    """策略数据包"""
    packet_id: str
    workflow_id: str = ""  # 添加兼容属性
    trigger_event: str
    target_return: float = 0.15
    risk_preference: str = "medium"

    # 输入数据
    intelligence_input: Dict = Field(default_factory=dict)
    market_context: Dict = Field(default_factory=dict)

    # 因子研发结果
    factor_research_results: Dict = Field(default_factory=dict)
    
    # 模型开发结果
    model_development_results: Dict = Field(default_factory=dict)
    
    # 回测分析结果
    backtest_results: Dict = Field(default_factory=dict)
    
    # 模型评估结果
    evaluation_results: Dict = Field(default_factory=dict)
    
    # 参数优化结果
    optimization_results: Dict = Field(default_factory=dict)
    
    # 策略生成结果
    strategy_generation_results: Dict = Field(default_factory=dict)
    
    # RD-Agent增强结果
    rd_agent_insights: Dict = Field(default_factory=dict)
    
    # 整合结果
    integrated_strategy: Dict = Field(default_factory=dict)
    overall_confidence: float = 0.0

class StrategyWorkflowCollection:
    """天璇星-策略架构师智能工作流集合"""
    
    def __init__(self, config: StrategyWorkflowConfig = None):
        self.config = config or StrategyWorkflowConfig()

        # 核心服务 - 恢复节点系统
        self.node_service = InvestmentNodeService()
        
        # 外部服务接口 (将在初始化时注入)
        self.factor_research_service = None
        self.model_development_service = None
        self.backtest_analysis_service = None
        self.model_evaluation_service = None
        self.parameter_optimization_service = None
        self.strategy_generation_service = None
        self.rd_agent_strategy_service = None

        # 增强服务
        self.disc_finllm_service = None
        self.cache_system = None
        
        # 工作流状态
        self.active_workflows: Dict[str, StrategyDataPacket] = {}
        self.workflow_history: List[StrategyDataPacket] = []

        # 工作流集合 - 为了兼容服务集成管理器
        self.workflows = {"default": self}

        # 添加初始化标志和注入服务字典
        self.initialized = True
        self.injected_services = {}

        # 自动注入服务
        self._auto_inject_services()

        logger.info("天璇星-策略架构师智能工作流集合初始化完成")

    def get_status(self) -> Dict[str, Any]:
        """获取工作流状态"""
        return {
            "initialized": self.initialized,
            "status": "active" if self.initialized else "inactive",
            "workflow_count": len(self.workflows),
            "service_count": len(self.injected_services),
            "timestamp": datetime.now().isoformat(),
            "version": "2.0.0"
        }

    def _auto_inject_services(self):
        """自动注入服务"""
        try:
            from backend.core.foundation.service_injection_manager import service_injection_manager

            # 异步注入服务
            import asyncio
            asyncio.create_task(self._async_inject_services(service_injection_manager))

        except Exception as e:
            logger.warning(f"  自动服务注入失败: {e}")

    async def _async_inject_services(self, injection_manager):
        """异步注入服务"""
        try:
            # inject_services_to_workflow是同步方法，不需要await
            injection_manager.inject_services_to_workflow(self)
            logger.info("  天璇星策略服务自动注入成功")
        except Exception as e:
            logger.error(f"  异步服务注入失败: {e}")

    async def create_workflow_node(self, node_key: str, node_type: str, content: Dict, metadata: Dict = None):
        """创建工作流节点的适配器方法"""
        try:
            # 创建一个通用节点来存储工作流数据
            from shared.infrastructure.investment_node_service import NodeType

            # 使用通用的策略生成节点类型
            generic_node = await self.node_service.create_node(
                node_type=NodeType.STRATEGY_GENERATION,
                key=node_key,
                instruction=f"处理{node_type}工作流步骤"
            )

            # 填充内容
            generic_node.content = content
            generic_node.confidence = content.get("confidence", 0.7)

            # 添加元数据
            if metadata:
                generic_node.rd_agent_insights.update(metadata)

            logger.info(f"创建工作流节点成功: {node_key}")
            return generic_node

        except Exception as e:
            logger.warning(f"创建工作流节点失败: {e}，继续执行")
            return None
    
    def inject_services(self, services: Dict[str, Any]):
        """注入外部服务"""
        self.factor_research_service = services.get("factor_research_service")
        self.model_development_service = services.get("model_development_service")
        self.backtest_analysis_service = services.get("backtest_analysis_service")
        self.model_evaluation_service = services.get("model_evaluation_service")
        self.parameter_optimization_service = services.get("parameter_optimization_service")
        self.strategy_generation_service = services.get("strategy_generation_service")
        self.rd_agent_strategy_service = services.get("rd_agent_strategy_service")

        # 注入新的DISC-FinLLM服务
        self.disc_finllm_service = services.get("disc_finllm_service")

        # 注入缓存系统
        self.cache_system = services.get("cache_system")

        logger.info("策略架构师服务注入完成（包含DISC-FinLLM和缓存系统）")

    def inject_real_services(self) -> bool:
        """注入真实服务"""
        try:
            from ..services.real_factor_research_service import RealFactorResearchService
            from ..services.real_model_development_service import RealModelDevelopmentService
            from ..services.real_backtest_analysis_service import RealBacktestAnalysisService
            from ..services.real_model_evaluation_service import (
                RealModelEvaluationService,
                RealParameterOptimizationService,
                RealStrategyGenerationService
            )
            from shared.infrastructure.deepseek_service import DeepSeekService

            # 注入真实服务
            self.factor_research_service = RealFactorResearchService()
            self.model_development_service = RealModelDevelopmentService()
            self.backtest_analysis_service = RealBacktestAnalysisService()
            self.model_evaluation_service = RealModelEvaluationService()
            self.parameter_optimization_service = RealParameterOptimizationService()
            self.strategy_generation_service = RealStrategyGenerationService()
            self.rd_agent_strategy_service = DeepSeekService()

            logger.info("  策略架构师真实服务注入完成")
            return True

        except Exception as e:
            logger.error(f"  策略架构师真实服务注入失败: {e}")
            return False
    
    @smart_retry(max_attempts=3, strategy=RetryStrategy.EXPONENTIAL_BACKOFF)
    async def execute_strategy_workflow(
        self,
        trigger_event: str = None,
        target_return: float = 0.15,
        risk_preference: str = "medium",
        intelligence_data: Dict = None,
        market_context: Dict = None
    ) -> StrategyDataPacket:
        """执行完整的策略工作流 - 增强版小闭环"""

        logger.info(f"  天璇星策略分析小闭环启动: {trigger_event}, 目标收益: {target_return}")

        # 启动小闭环管理 - 使用简化的闭环管理
        try:
            from backend.core.foundation.loop_manager import loop_manager, LoopType
        except ImportError:
            # 如果loop_manager不存在，使用简化的闭环管理
            class SimpleLoop:
                def __init__(self, loop_id, loop_name):
                    self.loop_id = loop_id
                    self.loop_name = loop_name
                    self.start_time = datetime.now()

            class SimpleLoopManager:
                def create_loop(self, **kwargs):
                    return SimpleLoop(f"loop_{int(datetime.now().timestamp())}", kwargs.get("loop_name", "default"))
                def complete_loop(self, loop_id, success=True, final_outputs=None):
                    logger.info(f"闭环完成: {loop_id}, 成功: {success}")

            loop_manager = SimpleLoopManager()
            class LoopType:
                ROLE_INTERNAL = "role_internal"

        small_loop = loop_manager.create_loop(
            loop_name="天璇星策略分析小闭环",
            loop_type=LoopType.ROLE_INTERNAL,
            context={
                "initiator": "tianxuan_star",
                "trigger_event": trigger_event,
                "target_return": target_return,
                "risk_preference": risk_preference,
                "timeout_seconds": 300
            }
        )

        try:
            # 1. 创建数据包
            packet_id = f"strategy_{int(datetime.now().timestamp() * 1000)}"
            data_packet = StrategyDataPacket(
                packet_id=packet_id,
                workflow_id=packet_id,  # 设置兼容属性
                trigger_event=trigger_event or "strategy_development",
                target_return=target_return,
                risk_preference=risk_preference
            )

            # 2. 存储输入数据
            if intelligence_data:
                data_packet.intelligence_input = intelligence_data
            if market_context:
                data_packet.market_context = market_context

            # 2. 存储活跃工作流
            self.active_workflows[data_packet.packet_id] = data_packet

            # 3. 执行工作流步骤
            if self.config.enable_factor_research:
                await self._execute_factor_research(data_packet)
            
            if self.config.enable_model_development:
                await self._execute_model_development(data_packet)
            
            if self.config.enable_backtest_analysis:
                await self._execute_backtest_analysis(data_packet)
            
            if self.config.enable_model_evaluation:
                await self._execute_model_evaluation(data_packet)
            
            if self.config.enable_parameter_optimization:
                await self._execute_parameter_optimization(data_packet)
            
            if self.config.enable_strategy_generation:
                await self._execute_strategy_generation(data_packet)
            
            if self.config.enable_rd_agent:
                await self._execute_rd_agent_enhancement(data_packet)
            
            # 4. 整合结果
            await self._integrate_strategy_results(data_packet)
            
            # 5. 移动到历史记录
            self.workflow_history.append(data_packet)
            del self.active_workflows[data_packet.packet_id]

            # 6. 完成小闭环
            loop_manager.complete_loop(
                small_loop.loop_id,
                success=True,
                final_outputs={
                    "strategy_id": data_packet.packet_id,
                    "overall_confidence": data_packet.overall_confidence,
                    "workflow_summary": data_packet.integrated_strategy.get("workflow_summary", {}),
                    "execution_time": (datetime.now() - small_loop.start_time).total_seconds() if small_loop.start_time else 0
                }
            )

            logger.info(f"  天璇星策略分析小闭环完成: {data_packet.packet_id}, 置信度: {data_packet.overall_confidence:.2f}")
            return data_packet

        except Exception as e:
            logger.error(f"  天璇星策略分析小闭环失败: {e}")

            # 失败时也要完成小闭环
            loop_manager.complete_loop(
                small_loop.loop_id,
                success=False,
                final_outputs={
                    "error": str(e),
                    "failure_point": "strategy_workflow_execution"
                }
            )

            # 保留在活跃工作流中以便调试
            raise
    
    async def _execute_factor_research(self, data_packet: StrategyDataPacket):
        """执行因子研发"""
        
        if not self.factor_research_service:
            logger.warning("因子研发服务未注入，跳过")
            return
        
        try:
            logger.info("执行因子研发节点")
            
            # 创建因子研发节点
            node_key = f"factor_research_{data_packet.packet_id}"
            node_type = "FactorResearchNode"
            
            # 执行因子研发
            research_context = {
                "research_objective": "寻找有效的选股因子",
                "market_environment": "normal",
                "target_universe": ["000001.SZ", "000002.SZ", "600036.SH", "600519.SH", "000858.SZ"]
            }
            research_result = await self.factor_research_service.research_new_factors(
                research_context=research_context
            )
            
            # 存储结果
            data_packet.factor_research_results = research_result

            # DISC-FinLLM增强因子研发
            if self.disc_finllm_service:
                try:
                    finllm_enhancement = await self.disc_finllm_service.enhance_factor_research(
                        factor_data=research_result,
                        market_context=data_packet.market_context or {}
                    )
                    data_packet.factor_research_results["finllm_enhancement"] = finllm_enhancement.result
                    logger.info("DISC-FinLLM因子研发增强完成")
                except Exception as e:
                    logger.warning(f"DISC-FinLLM因子研发增强失败: {e}")

            # RD-Agent增强 - 修复参数匹配
            if self.rd_agent_strategy_service:
                enhanced_content = await self.rd_agent_strategy_service.enhance_node_content(
                    research_result, "factor_research"
                )
                data_packet.factor_research_results.update(enhanced_content)
            
            # 创建投资节点
            await self.create_workflow_node(
                node_key=node_key,
                node_type=node_type,
                content=data_packet.factor_research_results,
                metadata={
                    "workflow_id": data_packet.packet_id,
                    "step": "factor_research",
                    "timestamp": datetime.now().isoformat()
                }
            )
            
            logger.info("因子研发节点执行完成")
            
        except Exception as e:
            logger.error(f"因子研发执行失败: {e}")
            data_packet.factor_research_results = {"error": str(e), "success": False}
    
    async def _execute_model_development(self, data_packet: StrategyDataPacket):
        """执行模型开发"""
        
        if not self.model_development_service:
            logger.warning("模型开发服务未注入，跳过")
            return
        
        try:
            logger.info("执行模型开发节点")
            
            # 从因子研发结果中提取特征
            features = self._extract_features_from_factors(data_packet.factor_research_results)
            
            # 执行模型开发
            development_result = await self.model_development_service.develop_models(
                features=features,
                model_types=["tree", "linear", "neural"]
            )
            
            # 存储结果
            data_packet.model_development_results = development_result

            # DISC-FinLLM优化模型开发
            if self.disc_finllm_service:
                try:
                    finllm_optimization = await self.disc_finllm_service.optimize_model_development(
                        model_data=development_result,
                        backreal_data=data_packet.backtest_results or {}
                    )
                    data_packet.model_development_results["finllm_optimization"] = finllm_optimization.result
                    logger.info("DISC-FinLLM模型开发优化完成")
                except Exception as e:
                    logger.warning(f"DISC-FinLLM模型开发优化失败: {e}")

            # 创建投资节点
            node_key = f"model_development_{data_packet.packet_id}"
            await self.create_workflow_node(
                node_key=node_key,
                node_type="ModelDevelopmentNode",
                content=data_packet.model_development_results,
                metadata={
                    "workflow_id": data_packet.packet_id,
                    "step": "model_development",
                    "features_count": len(features)
                }
            )
            
            logger.info("模型开发节点执行完成")
            
        except Exception as e:
            logger.error(f"模型开发执行失败: {e}")
            data_packet.model_development_results = {"error": str(e), "success": False}
    
    async def _execute_backtest_analysis(self, data_packet: StrategyDataPacket):
        """执行回测分析"""
        
        if not self.backtest_analysis_service:
            logger.warning("回测分析服务未注入，跳过")
            return
        
        try:
            logger.info("执行回测分析节点")
            
            # 生成策略名称
            strategy_name = f"Strategy_{data_packet.packet_id}"
            
            # 执行回测
            backtest_result = await self.backtest_analysis_service.run_backtest(
                strategy_name=strategy_name,
                start_date="2023-01-01",
                end_date=datetime.now().strftime("%Y-%m-%d"),
                initial_capital=1000000
            )
            
            # 存储结果
            data_packet.backtest_results = backtest_result
            
            # 创建投资节点
            node_key = f"backtest_analysis_{data_packet.packet_id}"
            await self.create_workflow_node(
                node_key=node_key,
                node_type="BacktestAnalysisNode",
                content=data_packet.backtest_results,
                metadata={
                    "workflow_id": data_packet.packet_id,
                    "step": "backtest_analysis",
                    "strategy_name": strategy_name
                }
            )
            
            logger.info("回测分析节点执行完成")
            
        except Exception as e:
            logger.error(f"回测分析执行失败: {e}")
            data_packet.backtest_results = {"error": str(e), "success": False}
    
    async def _execute_model_evaluation(self, data_packet: StrategyDataPacket):
        """执行模型评估"""
        
        if not self.model_evaluation_service:
            logger.warning("模型评估服务未注入，跳过")
            return
        
        try:
            logger.info("执行模型评估节点")
            
            # 获取最佳模型
            best_model = self._extract_best_model(data_packet.model_development_results)
            
            if best_model:
                # 执行模型评估
                evaluation_result = await self.model_evaluation_service.evaluate_model(
                    model_id=best_model.get("model_id", ""),
                    model_name=best_model.get("model_name", ""),
                    model_data=best_model
                )
                
                # 存储结果
                data_packet.evaluation_results = evaluation_result
            else:
                data_packet.evaluation_results = {"error": "没有可评估的模型", "success": False}
            
            # 创建投资节点
            node_key = f"model_evaluation_{data_packet.packet_id}"
            await self.create_workflow_node(
                node_key=node_key,
                node_type="ModelEvaluationNode",
                content=data_packet.evaluation_results,
                metadata={
                    "workflow_id": data_packet.packet_id,
                    "step": "model_evaluation"
                }
            )
            
            logger.info("模型评估节点执行完成")
            
        except Exception as e:
            logger.error(f"模型评估执行失败: {e}")
            data_packet.evaluation_results = {"error": str(e), "success": False}
    
    async def _execute_parameter_optimization(self, data_packet: StrategyDataPacket):
        """执行参数优化"""
        
        if not self.parameter_optimization_service:
            logger.warning("参数优化服务未注入，跳过")
            return
        
        try:
            logger.info("执行参数优化节点")
            
            # 获取最佳模型参数
            best_model = self._extract_best_model(data_packet.model_development_results)
            
            if best_model and "hyperparameters" in best_model:
                # 执行参数优化
                optimization_result = await self.parameter_optimization_service.optimize_parameters(
                    model_name=best_model.get("model_name", ""),
                    current_params=best_model["hyperparameters"],
                    target_metric=self.config.optimization_target,
                    max_iterations=self.config.max_optimization_iterations
                )
                
                # 存储结果
                data_packet.optimization_results = optimization_result
            else:
                data_packet.optimization_results = {"error": "没有可优化的参数", "success": False}
            
            # 创建投资节点
            node_key = f"parameter_optimization_{data_packet.packet_id}"
            await self.create_workflow_node(
                node_key=node_key,
                node_type="ParameterOptimizationNode",
                content=data_packet.optimization_results,
                metadata={
                    "workflow_id": data_packet.packet_id,
                    "step": "parameter_optimization"
                }
            )
            
            logger.info("参数优化节点执行完成")
            
        except Exception as e:
            logger.error(f"参数优化执行失败: {e}")
            data_packet.optimization_results = {"error": str(e), "success": False}
    
    async def _execute_strategy_generation(self, data_packet: StrategyDataPacket):
        """执行策略生成"""
        
        if not self.strategy_generation_service:
            logger.warning("策略生成服务未注入，跳过")
            return
        
        try:
            logger.info("执行策略生成节点")
            
            # 执行策略生成
            generation_result = await self.strategy_generation_service.generate_strategy(
                strategy_type="auto",
                market_condition="normal",
                risk_preference=data_packet.risk_preference,
                target_return=data_packet.target_return
            )
            
            # 存储结果
            data_packet.strategy_generation_results = generation_result
            
            # 创建投资节点
            node_key = f"strategy_generation_{data_packet.packet_id}"
            await self.create_workflow_node(
                node_key=node_key,
                node_type="StrategyGenerationNode",
                content=data_packet.strategy_generation_results,
                metadata={
                    "workflow_id": data_packet.packet_id,
                    "step": "strategy_generation"
                }
            )
            
            logger.info("策略生成节点执行完成")
            
        except Exception as e:
            logger.error(f"策略生成执行失败: {e}")
            data_packet.strategy_generation_results = {"error": str(e), "success": False}
    
    async def _execute_rd_agent_enhancement(self, data_packet: StrategyDataPacket):
        """执行RD-Agent增强"""
        
        if not self.rd_agent_strategy_service:
            logger.warning("RD-Agent策略服务未注入，跳过")
            return
        
        try:
            logger.info("执行RD-Agent增强节点")
            
            # 整合所有结果进行学习
            learning_data = {
                "factor_research": data_packet.factor_research_results,
                "model_development": data_packet.model_development_results,
                "backtest_analysis": data_packet.backtest_results,
                "evaluation": data_packet.evaluation_results,
                "optimization": data_packet.optimization_results,
                "strategy_generation": data_packet.strategy_generation_results
            }
            
            # 执行RD-Agent学习
            rd_insights = await self.rd_agent_strategy_service.learn_from_strategy_performance(
                strategy_data=learning_data,
                performance_data=data_packet.backtest_results.get("performance_summary", {}),
                market_data={"volatility": 0.2, "trend_strength": 0.5}
            )
            
            # 存储结果
            data_packet.rd_agent_insights = rd_insights
            
            # 创建投资节点
            node_key = f"rd_agent_enhancement_{data_packet.packet_id}"
            await self.create_workflow_node(
                node_key=node_key,
                node_type="RDAgentEnhancementNode",
                content=data_packet.rd_agent_insights,
                metadata={
                    "workflow_id": data_packet.packet_id,
                    "step": "rd_agent_enhancement"
                }
            )
            
            logger.info("RD-Agent增强节点执行完成")
            
        except Exception as e:
            logger.error(f"RD-Agent增强执行失败: {e}")
            data_packet.rd_agent_insights = {"error": str(e), "success": False}
    
    async def _integrate_strategy_results(self, data_packet: StrategyDataPacket):
        """整合策略结果"""

        logger.info("开始整合策略结果")

        # 检查缓存中是否有类似的策略结果
        cache_key = f"strategy_integration_{data_packet.risk_preference}_{data_packet.target_return}"
        if self.cache_system:
            cached_result = await self.cache_system.get(cache_key)
            if cached_result:
                logger.info("使用缓存的策略整合结果")
                data_packet.integrated_strategy = cached_result
                return
        
        # 计算整体置信度
        confidences = []
        
        # 收集各步骤的置信度
        if data_packet.factor_research_results.get("success"):
            confidences.append(0.8)  # 因子研发基础置信度
        
        if data_packet.model_development_results.get("success"):
            best_model = self._extract_best_model(data_packet.model_development_results)
            if best_model:
                confidences.append(best_model.get("confidence", 0.7))
        
        if data_packet.backtest_results.get("success"):
            backreal_data = data_packet.backtest_results.get("backtest_result", {})
            confidences.append(backreal_data.get("confidence", 0.7))
        
        if data_packet.evaluation_results.get("success"):
            eval_data = data_packet.evaluation_results.get("evaluation_result", {})
            confidences.append(eval_data.get("confidence", 0.7))
        
        if data_packet.strategy_generation_results.get("success"):
            strategy_data = data_packet.strategy_generation_results.get("generated_strategy", {})
            confidences.append(strategy_data.get("confidence", 0.7))
        
        # 计算加权平均置信度
        data_packet.overall_confidence = sum(confidences) / len(confidences) if confidences else 0.0
        
        # DISC-FinLLM策略整合增强
        if self.disc_finllm_service:
            try:
                finllm_integration = await self.disc_finllm_service.integrate_strategy_components(data_packet)

                # 整合最终策略（包含DISC-FinLLM增强）
                data_packet.integrated_strategy = {
                    "strategy_id": data_packet.packet_id,
                    "workflow_summary": {
                        "factor_research_success": data_packet.factor_research_results.get("success", False),
                        "model_development_success": data_packet.model_development_results.get("success", False),
                        "backtest_success": data_packet.backtest_results.get("success", False),
                        "evaluation_success": data_packet.evaluation_results.get("success", False),
                        "optimization_success": data_packet.optimization_results.get("success", False),
                        "strategy_generation_success": data_packet.strategy_generation_results.get("success", False),
                        "rd_agent_success": data_packet.rd_agent_insights.get("success", False),
                        "finllm_integration_success": True
                    },
                    "finllm_integration": finllm_integration.result,
                    "overall_confidence": max(data_packet.overall_confidence, finllm_integration.confidence),
                    "target_return": data_packet.target_return,
                    "risk_preference": data_packet.risk_preference,
                    "integration_timestamp": datetime.now().isoformat()
                }

                # 更新整体置信度
                data_packet.overall_confidence = max(data_packet.overall_confidence, finllm_integration.confidence)

                logger.info("DISC-FinLLM策略整合增强完成")

            except Exception as e:
                logger.warning(f"DISC-FinLLM策略整合失败: {e}")
                # 使用原始整合逻辑
                data_packet.integrated_strategy = {
                    "strategy_id": data_packet.packet_id,
                    "workflow_summary": {
                        "factor_research_success": data_packet.factor_research_results.get("success", False),
                        "model_development_success": data_packet.model_development_results.get("success", False),
                        "backtest_success": data_packet.backtest_results.get("success", False),
                        "evaluation_success": data_packet.evaluation_results.get("success", False),
                        "optimization_success": data_packet.optimization_results.get("success", False),
                        "strategy_generation_success": data_packet.strategy_generation_results.get("success", False),
                        "rd_agent_success": data_packet.rd_agent_insights.get("success", False)
                    },
                    "overall_confidence": data_packet.overall_confidence,
                    "target_return": data_packet.target_return,
                    "risk_preference": data_packet.risk_preference,
                    "integration_timestamp": datetime.now().isoformat()
                }
        else:
            # 原始整合逻辑
            data_packet.integrated_strategy = {
                "strategy_id": data_packet.packet_id,
                "workflow_summary": {
                    "factor_research_success": data_packet.factor_research_results.get("success", False),
                    "model_development_success": data_packet.model_development_results.get("success", False),
                    "backtest_success": data_packet.backtest_results.get("success", False),
                    "evaluation_success": data_packet.evaluation_results.get("success", False),
                    "optimization_success": data_packet.optimization_results.get("success", False),
                    "strategy_generation_success": data_packet.strategy_generation_results.get("success", False),
                    "rd_agent_success": data_packet.rd_agent_insights.get("success", False)
                },
                "overall_confidence": data_packet.overall_confidence,
                "target_return": data_packet.target_return,
                "risk_preference": data_packet.risk_preference,
                "integration_timestamp": datetime.now().isoformat()
            }

        # 缓存策略整合结果
        if self.cache_system and data_packet.overall_confidence > 0.7:
            await self.cache_system.set(
                cache_key,
                data_packet.integrated_strategy,
                ttl_seconds=3600,  # 1小时缓存
                importance_score=0.8,
                memory_related=True
            )
            logger.info("策略整合结果已缓存")

        logger.info(f"策略结果整合完成，整体置信度: {data_packet.overall_confidence:.2f}")
    
    def _extract_features_from_factors(self, factor_results: Dict[str, Any]) -> List[str]:
        """从因子研发结果中提取特征"""
        
        features = []
        
        if factor_results.get("success") and "factor_results" in factor_results:
            for factor in factor_results["factor_results"]:
                features.append(factor.get("factor_name", "unknown_factor"))
        
        # 如果没有因子，使用默认特征
        if not features:
            features = ["price_momentum", "volume_momentum", "rsi_signal", "macd_signal", "bollinger_signal"]
        
        return features
    
    def _extract_best_model(self, model_results: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """提取最佳模型"""
        
        if not model_results.get("success") or "best_model" not in model_results:
            return None
        
        return model_results["best_model"]
    
    async def create_strategy_report_message(self, data_packet: StrategyDataPacket) -> InvestmentMessage:
        """创建策略报告消息"""
        return InvestmentMessage.create_strategy_report(
            content=data_packet.integrated_strategy,
            confidence=data_packet.overall_confidence,
            rd_agent_insights=data_packet.rd_agent_insights
        )

# 全局工作流集合实例
strategy_workflow_collection = StrategyWorkflowCollection()
