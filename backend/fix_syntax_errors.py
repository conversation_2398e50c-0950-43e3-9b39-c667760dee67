#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复记忆集成脚本引入的语法错误
"""

import os
import re

def fix_syntax_errors_in_file(file_path: str) -> bool:
    """修复文件中的语法错误"""
    try:
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return False
        
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复缺少缩进的问题
        # 修复 }result = 的问题
        content = re.sub(r'(\s*}\s*)(\w+\s*=\s*await)', r'\1\n            \2', content)
        
        # 修复 }memories = 的问题  
        content = re.sub(r'(\s*}\s*)(\w+\s*=\s*await.*search_memories)', r'\1\n            \2', content)
        
        # 修复重复的导入和映射
        # 删除重复的导入
        lines = content.split('\n')
        cleaned_lines = []
        in_duplicate_section = False
        
        for i, line in enumerate(lines):
            # 检查是否是重复的导入和映射部分
            if 'from core.domain.memory.legendary.models import MessageType, MemoryPriority' in line and i > 0:
                # 检查前面是否已经有相同的导入
                prev_content = '\n'.join(lines[:i])
                if 'from core.domain.memory.legendary.models import MessageType, MemoryPriority' in prev_content:
                    in_duplicate_section = True
                    continue
            
            if in_duplicate_section:
                # 跳过重复的映射部分
                if '# 映射消息类型' in line or '# 映射优先级' in line:
                    continue
                elif 'message_type_mapping = {' in line:
                    # 跳过整个映射字典
                    brace_count = 1
                    continue
                elif in_duplicate_section and line.strip().startswith('"'):
                    continue
                elif in_duplicate_section and '}' in line and 'priority_mapping' not in line:
                    continue
                elif 'priority_mapping = {' in line:
                    # 跳过优先级映射
                    continue
                elif in_duplicate_section and line.strip() == '}':
                    in_duplicate_section = False
                    continue
            
            cleaned_lines.append(line)
        
        content = '\n'.join(cleaned_lines)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"  ✅ 语法错误修复成功: {os.path.basename(file_path)}")
        return True
        
    except Exception as e:
        print(f"  ❌ 语法错误修复失败: {os.path.basename(file_path)} - {e}")
        return False

def main():
    """主函数"""
    print('🔧 修复记忆集成脚本引入的语法错误')
    print('=' * 60)
    
    # 需要修复的文件
    files_to_fix = [
        'roles/tianxuan_star/services/tianxuan_automation_system.py',
        'roles/tianji_star/services/tianji_automation_system.py',
        'roles/tianquan_star/core/tianquan_automation_system.py',
        'roles/yuheng_star/services/yuheng_automation_system.py',
        'roles/kaiyang_star/services/kaiyang_automation_system.py',
        'roles/yaoguang_star/automation/quantitative_research_automation.py'
    ]
    
    success_count = 0
    
    for file_path in files_to_fix:
        print(f'\n🔍 修复 {os.path.basename(file_path)}...')
        
        if fix_syntax_errors_in_file(file_path):
            success_count += 1
    
    print(f'\n📋 修复完成:')
    print(f'  成功: {success_count}/{len(files_to_fix)}')
    print(f'  失败: {len(files_to_fix) - success_count}/{len(files_to_fix)}')

if __name__ == "__main__":
    main()
