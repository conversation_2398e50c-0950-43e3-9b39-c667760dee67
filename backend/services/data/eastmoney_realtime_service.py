#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
东方财富实时股票数据服务
"""

import requests
import json
import re
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import asyncio
import aiohttp
import functools

logger = logging.getLogger(__name__)


import functools

def ensure_session_cleanup(func):
    """确保异步会话清理的装饰器"""
    @functools.wraps(func)
    async def wrapper(self, *args, **kwargs):
        try:
            return await func(self, *args, **kwargs)
        finally:
            # 确保会话被正确清理
            if hasattr(self, 'session') and self.session and not self.session.closed:
                try:
                    await self.session.close()
                    self.session = None
                except Exception as e:
                    logger.warning(f"清理会话时出现警告: {e}")
    return wrapper

class EastMoneyRealtimeService:
    """东方财富实时股票数据服务"""
    
    def __init__(self):
        self.base_url = "https://push2.eastmoney.com/api/qt/ulist/get"
        self.session = None
        
        # 字段映射
        self.field_mapping = {
            'f2': 'current_price',      # 当前最新价格
            'f3': 'change_pct',         # 涨跌幅度
            'f4': 'change_amount',      # 涨跌额
            'f5': 'volume',             # 成交量
            'f6': 'turnover',           # 成交额
            'f12': 'stock_code',        # 股票代码
            'f13': 'market',            # 市场
            'f14': 'stock_name',        # 股票名称
            'f15': 'high',              # 最高
            'f16': 'low',               # 最低
            'f17': 'open',              # 今开
            'f18': 'prev_close',        # 昨收
            'f20': 'total_market_cap',  # 总市值
            'f21': 'float_market_cap',  # 流通市值
            'f350': 'limit_up',         # 涨停价
            'f351': 'limit_down'        # 跌停价
        }
        
        logger.info("东方财富实时数据服务初始化完成")
    
    async def initialize(self):
        """初始化异步会话"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        logger.info("东方财富实时数据服务异步会话初始化完成")
    
    async def close(self):
        """关闭异步会话"""
        if self.session:
            await self.session.close()
            self.session = None

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    def convert_stock_code_to_secid(self, stock_code: str) -> str:
        """
        将股票代码转换为secid格式
        规则：
        - 深交所（000、002、300开头）：0.股票代码
        - 上交所（600、601、603、688开头）：1.股票代码
        """
        if stock_code.startswith(('000', '002', '300')):
            return f"0.{stock_code}"
        elif stock_code.startswith(('600', '601', '603', '688')):
            return f"1.{stock_code}"
        else:
            # 默认深交所
            return f"0.{stock_code}"
    
    def parse_jsonp_response(self, response_text: str) -> Dict[str, Any]:
        """解析JSONP响应"""
        try:
            # 提取JSON部分
            match = re.search(r'jQuery\d+_\d+\((.*)\)', response_text)
            if match:
                json_str = match.group(1)
                return json.loads(json_str)
            else:
                logger.error("无法解析JSONP响应")
                return {}
        except Exception as e:
            logger.error(f"解析JSONP响应失败: {e}")
            return {}
    
    @ensure_session_cleanup
    async def get_realtime_data(self, stock_codes: List[str]) -> List[Dict[str, Any]]:
        """
        获取实时股票数据
        
        Args:
            stock_codes: 股票代码列表，最多20个
            
        Returns:
            股票实时数据列表
        """
        try:
            if not stock_codes:
                return []
            
            # 限制最多20个股票
            stock_codes = stock_codes[:20]
            
            # 转换为secids格式
            secids = [self.convert_stock_code_to_secid(code) for code in stock_codes]
            secids_str = ','.join(secids)
            
            # 构建请求参数
            params = {
                'fltt': '1',
                'invt': '2',
                'cb': f'jQuery{datetime.now().microsecond}_{int(datetime.now().timestamp() * 1000)}',
                'fields': 'f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f350,f351',
                'secids': secids_str,
                'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                'pn': '1',
                'np': '1',
                'pz': str(len(stock_codes)),
                'dect': '1',
                'wbp2u': '|0|0|0|web',
                '_': str(int(datetime.now().timestamp() * 1000))
            }
            
            # 发送请求
            if not self.session:
                await self.initialize()
            
            async with self.session.get(self.base_url, params=params) as response:
                if response.status == 200:
                    response_text = await response.text()
                    data = self.parse_jsonp_response(response_text)
                    
                    if data.get('rc') == 0 and 'data' in data:
                        return self.format_stock_data(data['data']['diff'])
                    else:
                        logger.error(f"API返回错误: {data}")
                        return []
                else:
                    logger.error(f"HTTP请求失败: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"获取实时股票数据失败: {e}")
            return []
    
    def format_stock_data(self, raw_data: List[Dict]) -> List[Dict[str, Any]]:
        """格式化股票数据"""
        formatted_data = []
        
        for item in raw_data:
            try:
                formatted_item = {
                    'stock_code': item.get('f12', ''),
                    'stock_name': item.get('f14', ''),
                    'current_price': item.get('f2', 0) / 100,  # 价格需要除以100
                    'change_pct': item.get('f3', 0) / 100,     # 涨跌幅需要除以100
                    'change_amount': item.get('f4', 0) / 100,  # 涨跌额需要除以100
                    'volume': item.get('f5', 0),               # 成交量（手）
                    'turnover': item.get('f6', 0),             # 成交额（元）
                    'open': item.get('f17', 0) / 100,          # 今开
                    'high': item.get('f15', 0) / 100,          # 最高
                    'low': item.get('f16', 0) / 100,           # 最低
                    'prev_close': item.get('f18', 0) / 100,    # 昨收
                    'total_market_cap': item.get('f20', 0),    # 总市值
                    'float_market_cap': item.get('f21', 0),    # 流通市值
                    'limit_up': item.get('f350', 0) / 100,     # 涨停价
                    'limit_down': item.get('f351', 0) / 100,   # 跌停价
                    'market': '深交所' if item.get('f13', 0) == 0 else '上交所',
                    'timestamp': datetime.now().isoformat(),
                    'data_source': 'eastmoney_realtime'
                }
                
                formatted_data.append(formatted_item)
                
            except Exception as e:
                logger.error(f"格式化股票数据失败: {e}, 原始数据: {item}")
                continue
        
        return formatted_data
    
    async def get_single_stock_data(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """获取单只股票的实时数据"""
        try:
            data_list = await self.get_realtime_data([stock_code])
            return data_list[0] if data_list else None
        except Exception as e:
            logger.error(f"获取单只股票数据失败 {stock_code}: {e}")
            return None
    
    def get_supported_stock_codes(self) -> List[str]:
        """获取支持的股票代码格式"""
        return [
            "深交所主板: 000xxx",
            "深交所中小板: 002xxx", 
            "深交所创业板: 300xxx",
            "上交所主板: 600xxx, 601xxx, 603xxx",
            "上交所科创板: 688xxx"
        ]


    async def get_realtime_data_with_retry(self, stock_codes: List[str], max_retries: int = 3) -> List[Dict[str, Any]]:
        """获取实时股票数据 - 带重试机制"""
        for attempt in range(max_retries):
            try:
                result = await self.get_realtime_data(stock_codes)
                if result:  # 如果有数据返回
                    return result
                else:
                    logger.warning(f"第{attempt + 1}次尝试未获取到数据，准备重试...")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(1)  # 等待1秒后重试
            except Exception as e:
                logger.error(f"第{attempt + 1}次尝试失败: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(2)  # 等待2秒后重试
                else:
                    # 最后一次失败，返回空数据
                    logger.error(f"所有重试失败，返回空数据")
                    return []
        
        return []
    
    async def get_historical_data(self, stock_code: str, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取历史数据 - 新增方法"""
        try:
            # 直接使用本地数据库获取历史数据
            # 避免循环导入，直接实现本地数据库查询
            
            if not start_date:
                start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            if not end_date:
                end_date = datetime.now().strftime('%Y-%m-%d')
            
            # 直接使用本地数据库获取历史数据
            return await self._get_local_historical_data(stock_code, start_date, end_date)
                
        except Exception as e:
            logger.error(f"❌ 获取历史数据异常: {e}")
            return {"success": False, "error": str(e)}
    
    async def _get_local_historical_data(self, stock_code: str, start_date: str, end_date: str) -> Dict[str, Any]:
        """从本地数据库获取历史数据"""
        try:
            import sqlite3
            import pandas as pd
            
            # 连接股票数据库
            db_path = "backend/data/stock_database.db"
            if not os.path.exists(db_path):
                return {"success": False, "error": "本地数据库不存在"}
            
            conn = sqlite3.connect(db_path)
            
            # 查询历史数据
            query = f"""
            SELECT date, open, high, low, close, volume, amount
            FROM stock_{stock_code.replace('.', '_')}
            WHERE date BETWEEN ? AND ?
            ORDER BY date
            """
            
            df = pd.read_sql_query(query, conn, params=[start_date, end_date])
            conn.close()
            
            if not df.empty:
                return {
                    "success": True,
                    "data": df.to_dict('records'),
                    "count": len(df),
                    "source": "local_database"
                }
            else:
                return {"success": False, "error": "本地数据库无数据"}
                
        except Exception as e:
            logger.error(f"本地数据库查询失败: {e}")
            return {"success": False, "error": str(e)}

# 创建全局实例
eastmoney_service = EastMoneyRealtimeService()

async def test_eastmoney_service():
    """测试东方财富服务"""
    try:
        await eastmoney_service.initialize()
        
        # 测试股票代码
        test_codes = ['000001', '000002', '600036', '600519', '300750']
        
        print("🔍 测试东方财富实时数据服务...")
        data = await eastmoney_service.get_realtime_data(test_codes)
        
        if data:
            print(f" 成功获取 {len(data)} 只股票的实时数据")
            for stock in data:
                print(f"   {stock['stock_code']} {stock['stock_name']}: ¥{stock['current_price']:.2f} ({stock['change_pct']:+.2f}%)")
        else:
            print(" 未获取到数据")
            
        await eastmoney_service.close()
        
    except Exception as e:
        print(f" 测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_eastmoney_service())
